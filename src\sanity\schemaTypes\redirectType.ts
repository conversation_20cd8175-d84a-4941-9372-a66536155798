import { defineField, defineType, SanityDocumentLike } from "sanity";
import { LinkIcon } from "@sanity/icons";

function isValidInternalPath(value: string | undefined) {
  if (!value) {
    return "Value is required";
  } else if (!value.startsWith("/")) {
    return "Internal paths must start with /";
  }
  return true;
}

function isValidUrl(value: string | undefined) {
  try {
    new URL(value || "");
    return true;
  } catch {
    return "Invalid URL";
  }
}

export const redirectType = defineType({
  name: "redirect",
  title: "Redirect",
  type: "document",
  icon: LinkIcon,
  validation: (Rule) =>
    Rule.custom((doc: SanityDocumentLike | undefined) => {
      if (doc && doc.source === doc.destination) {
        return ["source", "destination"].map((field) => ({
          message: "Source and destination cannot be the same",
          path: [field],
        }));
      }

      return true;
    }),
  fields: [
    defineField({
      name: "source",
      title: "Source Path",
      description: "The path to redirect from (must start with /)",
      type: "string",
      validation: (Rule) => Rule.required().custom(isValidInternalPath),
    }),
    defineField({
      name: "destination",
      title: "Destination",
      description: "The URL or path to redirect to",
      type: "string",
      validation: (Rule) =>
        Rule.required().custom((value: string | undefined) => {
          const urlValidation = isValidUrl(value);
          const pathValidation = isValidInternalPath(value);

          if (urlValidation === true || pathValidation === true) {
            return true;
          }
          return typeof urlValidation === "boolean"
            ? urlValidation
            : pathValidation;
        }),
    }),
    defineField({
      name: "permanent",
      title: "Permanent Redirect",
      description: "Should the redirect be permanent (301) or temporary (302)",
      type: "boolean",
      initialValue: true,
    }),
    defineField({
      name: "isEnabled",
      title: "Enabled",
      description: "Toggle this redirect on or off",
      type: "boolean",
      initialValue: true,
    }),
  ],
  preview: {
    select: {
      source: "source",
      destination: "destination",
      permanent: "permanent",
      isEnabled: "isEnabled",
    },
    prepare({ source, destination, permanent, isEnabled }) {
      return {
        title: `${source} → ${destination}`,
        subtitle: `${permanent ? "301" : "302"} ${isEnabled ? "✓" : "✗"}`,
      };
    },
  },
});
