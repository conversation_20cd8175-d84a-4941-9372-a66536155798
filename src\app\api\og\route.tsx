import { client } from "@/sanity/lib/client";
import { OG_IMAGE_QUERY } from "@/sanity/lib/queries";
import { notFound } from "next/navigation";
import { ImageResponse } from "next/og";

export const runtime = "edge";

async function loadGoogleFont(font: string, text: string) {
  try {
    const url = `https://fonts.googleapis.com/css2?family=${font}&text=${encodeURIComponent(text)}`;
    const css = await (await fetch(url)).text();
    const resource = css.match(
      /src: url\((.+)\) format\('(opentype|truetype)'\)/
    );

    if (resource) {
      const response = await fetch(resource[1]);
      if (response.status == 200) {
        return await response.arrayBuffer();
      }
    }
  } catch (error) {
    console.error("Failed to load Google Font:", error);
  }

  // Return null if font loading fails - ImageResponse will use default font
  return null;
}

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const id = searchParams.get("id");

  if (!id) {
    notFound();
  }

  let data;

  // Handle test case
  if (id === "test-sample") {
    data = {
      title: "Dynamic Open Graph Images Test",
      image: null, // This will use the default gradient
    };
  } else {
    data = await client.fetch(OG_IMAGE_QUERY, { id });

    if (!data) {
      notFound();
    }
  }

  const vibrantBackground =
    data?.image?.metadata?.palette?.vibrant?.background ?? "#3B82F6";
  const darkVibrantBackground =
    data?.image?.metadata?.palette?.darkVibrant?.background ?? "#3B82F6";

  const text = data.title || "";

  // Load font data
  const fontData = await loadGoogleFont("Inter", text);

  return new ImageResponse(
    (
      <div
        tw="flex w-full h-full relative"
        style={{
          background: `linear-gradient(135deg, ${vibrantBackground} 0%, ${darkVibrantBackground} 100%)`,
        }}
      >
        {/* Content container */}
        <div tw="flex flex-row w-full h-full relative">
          {/* Text content */}
          <div tw="flex-1 flex items-center px-10">
            <h1 tw="text-7xl tracking-tight leading-none text-white leading-tight">
              {text}
            </h1>
          </div>

          {/* Image container */}
          {data.image?.url && (
            <div tw="flex w-[500px] h-[630px] overflow-hidden">
              {/* eslint-disable-next-line @next/next/no-img-element */}
              <img
                src={data.image.url}
                alt=""
                tw="w-full h-full object-cover"
              />
            </div>
          )}
        </div>
      </div>
    ),
    {
      width: 1200,
      height: 630,
      fonts: fontData
        ? [
            {
              name: "Inter",
              data: fontData,
              weight: 400,
              style: "normal",
            },
          ]
        : [],
    }
  );
}
