import { FAQPage, WithContext } from "schema-dts";

interface FAQ {
  _id: string;
  title: string | null;
  text: string;
}

export function generateFAQJsonLd(faqs: FAQ[]): WithContext<FAQPage> {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqs
      .filter((faq) => faq.title && faq.text) // Filter out FAQs without title or text
      .map((faq) => ({
        "@type": "Question",
        name: faq.title!,
        acceptedAnswer: {
          "@type": "Answer",
          text: faq.text,
        },
      })),
  };
}
