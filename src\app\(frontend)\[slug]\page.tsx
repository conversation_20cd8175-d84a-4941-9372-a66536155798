import type { Metadata } from "next";
import { PageBuilder } from "@/components/PageBuilder";
import { sanityFetch } from "@/sanity/lib/live";
import { PAGE_QUERY } from "@/sanity/lib/queries";
import { urlFor } from "@/sanity/lib/image";
import JsonLd from "@/components/JsonLd";
import { generateFAQJsonLd } from "@/sanity/lib/jsonLd";

type RouteProps = {
  params: Promise<{ slug: string }>;
};

const getPage = async (params: RouteProps["params"]) =>
  sanityFetch({
    query: PAGE_QUERY,
    params: await params,
  });

export async function generateMetadata({
  params,
}: RouteProps): Promise<Metadata> {
  const { data: page } = await getPage(params);

  if (!page) {
    return {};
  }

  const metadata: Metadata = {
    metadataBase: new URL("https://chipsxp.com"),
    title: page.seo.title,
    description: page.seo.description,
  };

  metadata.openGraph = {
    images: {
      url: page.seo.image
        ? urlFor(page.seo.image).width(1200).height(630).url()
        : `/api/og?id=${page._id}`,
      width: 1200,
      height: 630,
    },
  };

  if (page.seo.noIndex) {
    metadata.robots = "noindex";
  }

  return metadata;
}

export default async function Page({ params }: RouteProps) {
  const { data: page } = await getPage(params);

  if (!page?.content) {
    return null;
  }

  // Extract FAQs from page content for JSON-LD
  const faqBlocks = page.content.filter((block) => block._type === "faqs");
  const allFaqs = faqBlocks.flatMap((block) =>
    block._type === "faqs" && Array.isArray(block.faqs)
      ? block.faqs.filter((faq) => faq && faq._id && faq.title && faq.text)
      : []
  );

  return (
    <>
      {allFaqs.length > 0 && <JsonLd data={generateFAQJsonLd(allFaqs)} />}
      <PageBuilder
        documentId={page._id}
        documentType={page._type}
        content={page.content}
      />
    </>
  );
}
