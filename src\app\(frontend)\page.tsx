import { PageBuilder } from "@/components/PageBuilder";
import { sanityFetch } from "@/sanity/lib/live";
import { HOME_PAGE_QUERY } from "@/sanity/lib/queries";
import type { PAGE_QUERYResult } from "@/sanity/types";
import JsonLd from "@/components/JsonLd";
import { generateFAQJsonLd } from "@/sanity/lib/jsonLd";

type PageBuilderContent = NonNullable<PAGE_QUERYResult>["content"];

interface HomePageData {
  homePage?: {
    _id: string;
    _type: string;
    content?: PageBuilderContent;
  } | null;
}

export default async function Page() {
  const { data: page } = await sanityFetch({
    query: HOME_PAGE_QUERY,
  });

  const homeData = page as HomePageData;

  if (!homeData?.homePage?.content) {
    return null;
  }

  // Extract FAQs from home page content for JSON-LD
  const faqBlocks = homeData.homePage.content.filter(
    (block) => block._type === "faqs"
  );
  const allFaqs = faqBlocks.flatMap((block) =>
    block._type === "faqs" && Array.isArray(block.faqs)
      ? block.faqs.filter((faq) => faq && faq._id && faq.title && faq.text)
      : []
  );

  return (
    <>
      {allFaqs.length > 0 && <JsonLd data={generateFAQJsonLd(allFaqs)} />}
      <PageBuilder
        documentId={homeData.homePage._id}
        documentType={homeData.homePage._type}
        content={homeData.homePage.content}
      />
    </>
  );
}
