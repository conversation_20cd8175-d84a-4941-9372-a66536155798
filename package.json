{"name": "nextjs-sanity", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prebuild": "sanity typegen generate", "extract": "sanity schema extract --path=./src/sanity/extract.json"}, "dependencies": {"@sanity/asset-utils": "^2.2.1", "@sanity/assist": "^5.0.0", "@sanity/icons": "^3.7.4", "@sanity/image-url": "^1.1.0", "@sanity/types": "^4.0.1", "@sanity/vision": "^4.0.1", "@sanity/visual-editing": "^2.15.3", "dayjs": "^1.11.13", "next": "15.4.2", "next-sanity": "^10.0.1", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-is": "^19.1.0", "sanity": "^4.0.1", "schema-dts": "^1.1.5", "styled-components": "^6.1.19"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.2", "tailwindcss": "^4.1.11", "typescript": "^5"}}