import { Metadata } from "next";

export const metadata: Metadata = {
  metadataBase: new URL("https://acme.com"),
  title: "OG Image Test Page",
  description: "Testing dynamic Open Graph image generation",
  openGraph: {
    title: "OG Image Test Page",
    description: "Testing dynamic Open Graph image generation",
    type: "website",
    images: [
      {
        url: `/api/og?id=test-sample`,
        width: 1200,
        height: 630,
        alt: "Dynamic Open Graph Images Test",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "OG Image Test Page",
    description: "Testing dynamic Open Graph image generation",
    images: [`/api/og?id=test-sample`],
  },
};

export default function TestOGPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-4xl font-bold mb-6">OG Image Test Page</h1>

      <div className="space-y-4">
        <p>This page tests the dynamic Open Graph image generation.</p>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">Test URLs:</h2>
          <ul className="space-y-2">
            <li>
              <strong>Direct OG API:</strong>{" "}
              <a
                href="/api/og?id=test-sample"
                target="_blank"
                className="text-blue-600 hover:underline"
              >
                /api/og?id=test-sample
              </a>
            </li>
            <li>
              <strong>This page&apos;s metadata:</strong> Check page source for
              og:image meta tag
            </li>
          </ul>
        </div>

        <div className="bg-blue-50 p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">How to Test:</h2>
          <ol className="list-decimal list-inside space-y-1">
            <li>
              Click the &quot;Direct OG API&quot; link above to see the
              generated image
            </li>
            <li>View page source to see the og:image meta tag</li>
            <li>Use social media debugging tools to test sharing</li>
            <li>
              Replace &quot;test-sample&quot; with real document IDs from your
              Sanity content
            </li>
          </ol>
        </div>

        <div className="bg-green-50 p-4 rounded">
          <h2 className="text-xl font-semibold mb-2">
            Social Media Testing Tools:
          </h2>
          <ul className="space-y-2">
            <li>
              <strong>Facebook:</strong>{" "}
              <a
                href="https://developers.facebook.com/tools/debug/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                Facebook Sharing Debugger
              </a>
            </li>
            <li>
              <strong>Twitter:</strong>{" "}
              <a
                href="https://cards-dev.twitter.com/validator"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                Twitter Card Validator
              </a>
            </li>
            <li>
              <strong>LinkedIn:</strong>{" "}
              <a
                href="https://www.linkedin.com/post-inspector/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                LinkedIn Post Inspector
              </a>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
}
