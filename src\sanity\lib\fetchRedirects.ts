import { client } from "./client";
import { REDIRECTS_QUERY } from "./queries";

export interface Redirect {
  source: string;
  destination: string;
  permanent: boolean;
}

export async function fetchRedirects(): Promise<Redirect[]> {
  try {
    const redirects = await client.fetch(REDIRECTS_QUERY);
    return redirects || [];
  } catch (error) {
    console.error("Error fetching redirects:", error);
    return [];
  }
}
